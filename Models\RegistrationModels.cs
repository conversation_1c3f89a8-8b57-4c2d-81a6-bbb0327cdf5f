using System.ComponentModel.DataAnnotations;

namespace PasswordHistoryValidator.Models;

/// <summary>
/// Request model for user registration operations.
/// Contains all fields required for creating a new user account.
/// </summary>
public class UserRegistrationRequest
{
    [Required(ErrorMessage = "Email is required")]
    [EmailAddress(ErrorMessage = "Invalid email format")]
    public string Email { get; set; } = string.Empty;
    
    [Required(ErrorMessage = "Password is required")]
    [MinLength(8, ErrorMessage = "Password must be at least 8 characters")]
    public string Password { get; set; } = string.Empty;
    
    [Required(ErrorMessage = "First name is required")]
    public string FirstName { get; set; } = string.Empty;
    
    [Required(ErrorMessage = "Last name is required")]
    public string LastName { get; set; } = string.Empty;
    
    [Required(ErrorMessage = "Application name is required")]
    public string ApplicationName { get; set; } = string.Empty;
    
    public string CorrelationId { get; set; } = Guid.NewGuid().ToString();
}

/// <summary>
/// Request model for validating invitation tokens during registration.
/// </summary>
public class InvitationValidationRequest
{
    [Required(ErrorMessage = "Token is required")]
    public string Token { get; set; } = string.Empty;
    
    [Required(ErrorMessage = "Verification code is required")]
    public string VerificationCode { get; set; } = string.Empty;
    
    public string CorrelationId { get; set; } = Guid.NewGuid().ToString();
}
