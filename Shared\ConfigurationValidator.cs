using Microsoft.Extensions.Configuration;

namespace PasswordHistoryValidator.Shared;


public static class ConfigurationValidator
{
    private static readonly string[] RequiredConfigurationKeys =
    {

        "SendGrid:ApiKey",
        "SendGrid:FromEmail",


        "EntraExternalID:TenantId",
        "EntraExternalID:ClientId",
        "EntraExternalID:ClientSecret",
        "EntraExternalID:DefaultDomain",


        "PasswordReset:BaseUrl",
        "AccountRegistration:BaseUrl",
        "ApplicationName"
    };


    public static void ValidateRequiredConfiguration(IConfiguration configuration)
    {
        var missingKeys = new List<string>();


        foreach (var key in RequiredConfigurationKeys)
        {
            if (string.IsNullOrEmpty(configuration[key]))
            {
                missingKeys.Add(key);
            }
        }

        // Special case: Storage requires either AzureWebJobsStorage OR Storage:ConnectionString
        var azureWebJobsStorage = configuration["AzureWebJobsStorage"];
        var storageConnectionString = configuration["Storage:ConnectionString"];
        if (string.IsNullOrEmpty(azureWebJobsStorage) && string.IsNullOrEmpty(storageConnectionString))
        {
            missingKeys.Add("AzureWebJobsStorage OR Storage:ConnectionString");
        }

        // Throw exception if any required configuration is missing
        if (missingKeys.Count > 0)
        {
            var errorMessage = "Required configuration values are missing:\n\n" +
                              string.Join("\n", missingKeys.Select(k => $"• {k}")) +
                              "\n\nPlease configure these values in local.settings.json or Azure App Settings.";

            throw new InvalidOperationException(errorMessage);
        }
    }
}