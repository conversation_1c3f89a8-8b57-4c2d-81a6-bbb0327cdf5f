using Microsoft.Extensions.Configuration;

namespace PasswordHistoryValidator.Shared;

/// <summary>
/// Simple configuration validation for required values at startup.
/// Validates that all essential configuration keys are present and throws clear error messages if missing.
/// </summary>
public static class ConfigurationValidator
{
    private static readonly string[] RequiredConfigurationKeys =
    {
        // SendGrid Configuration
        "SendGrid:ApiKey",
        "SendGrid:FromEmail",

        // Entra External ID Configuration
        "EntraExternalID:TenantId",
        "EntraExternalID:ClientId",
        "EntraExternalID:ClientSecret",
        "EntraExternalID:DefaultDomain",

        // Application URLs
        "PasswordReset:BaseUrl",
        "AccountRegistration:BaseUrl",
        "ApplicationName"
    };

    /// <summary>
    /// Validates that all required configuration values are present.
    /// Throws InvalidOperationException with clear error message if any are missing.
    /// </summary>
    /// <param name="configuration">The application configuration</param>
    /// <exception cref="InvalidOperationException">Thrown when required configuration is missing</exception>
    public static void ValidateRequiredConfiguration(IConfiguration configuration)
    {
        var missingKeys = new List<string>();

        // Check all required keys
        foreach (var key in RequiredConfigurationKeys)
        {
            if (string.IsNullOrEmpty(configuration[key]))
            {
                missingKeys.Add(key);
            }
        }

        // Special case: Storage requires either AzureWebJobsStorage OR Storage:ConnectionString
        var azureWebJobsStorage = configuration["AzureWebJobsStorage"];
        var storageConnectionString = configuration["Storage:ConnectionString"];
        if (string.IsNullOrEmpty(azureWebJobsStorage) && string.IsNullOrEmpty(storageConnectionString))
        {
            missingKeys.Add("AzureWebJobsStorage OR Storage:ConnectionString");
        }

        // Throw exception if any required configuration is missing
        if (missingKeys.Count > 0)
        {
            var errorMessage = "Required configuration values are missing:\n\n" +
                              string.Join("\n", missingKeys.Select(k => $"• {k}")) +
                              "\n\nPlease configure these values in local.settings.json or Azure App Settings.";

            throw new InvalidOperationException(errorMessage);
        }
    }
}