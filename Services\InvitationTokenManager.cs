using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Caching.Memory;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using PasswordHistoryValidator;
using PasswordHistoryValidator.Shared;

namespace PasswordHistoryValidator.Services;

public class InvitationTokenManager
{
    private readonly ILogger<InvitationTokenManager> _logger;
    private readonly BlobServiceClient _blobServiceClient;
    private readonly IMemoryCache _cache;
    private readonly JsonSerializerOptions _jsonOptions;
    private readonly ApplicationOptions _applicationOptions;

    private const string ContainerName = "invitationtokens";

    public InvitationTokenManager(
        ILogger<InvitationTokenManager> logger,
        BlobServiceClient blobServiceClient,
        IMemoryCache cache,
        JsonSerializerOptions jsonOptions,
        IOptions<ApplicationOptions> applicationOptions)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _blobServiceClient = blobServiceClient ?? throw new ArgumentNullException(nameof(blobServiceClient));
        _cache = cache ?? throw new ArgumentNullException(nameof(cache));
        _jsonOptions = jsonOptions ?? throw new ArgumentNullException(nameof(jsonOptions));
        _applicationOptions = applicationOptions?.Value ?? throw new ArgumentNullException(nameof(applicationOptions));
    }

    private async Task<InvitationTokenData?> GetTokenDataFromBlob(string blobName)
    {
        try
        {
            var containerClient = _blobServiceClient.GetBlobContainerClient(ContainerName);
            var blobClient = containerClient.GetBlobClient(blobName);

            if (!await blobClient.ExistsAsync())
                return null;

            var downloadResult = await blobClient.DownloadContentAsync();
            var tokenJson = downloadResult.Value.Content.ToString();
            return JsonSerializer.Deserialize<InvitationTokenData>(tokenJson, _jsonOptions);
        }
        catch (Exception)
        {
            return null;
        }
    }

    private async Task<bool> UpdateTokenDataInBlob(string blobName, InvitationTokenData tokenData)
    {
        try
        {
            var containerClient = _blobServiceClient.GetBlobContainerClient(ContainerName);
            var blobClient = containerClient.GetBlobClient(blobName);

            var jsonData = JsonSerializer.Serialize(tokenData, _jsonOptions);
            using var stream = new MemoryStream(Encoding.UTF8.GetBytes(jsonData));

            var metadata = new Dictionary<string, string>
            {
                { "createdUtc", tokenData.CreatedUtc.ToString("o") },
                { "expiresUtc", tokenData.ExpiresUtc.ToString("o") },
                { "email", tokenData.Email },
                { "used", tokenData.Used ? "true" : "false" },
                { "isSuperseded", tokenData.IsSuperseded ? "true" : "false" },
                { "verificationCode", tokenData.VerificationCode } // Add verification code to metadata for indexing
            };

            var options = new BlobUploadOptions
            {
                Metadata = metadata,
                HttpHeaders = new BlobHttpHeaders { ContentType = "application/json" }
            };

            await blobClient.UploadAsync(stream, options);
            
            
            return true;
        }
        catch (Exception)
        {
            return false;
        }
    }

    private (bool IsValid, string ErrorMessage) ValidateTokenData(InvitationTokenData? tokenData, string verificationCode = "")
    {
        if (tokenData == null)
            return (false, "Invalid or expired invitation token");

        if (tokenData.Used)
            return (false, "Invitation token has already been used");

        if (tokenData.IsSuperseded)
            return (false, "This invitation has been replaced by a newer one");

        if (DateTime.UtcNow > tokenData.ExpiresUtc)
            return (false, "Invitation token has expired");

        if (!string.IsNullOrEmpty(verificationCode) && tokenData.VerificationCode != verificationCode)
            return (false, "Invalid verification code");

        return (true, string.Empty);
    }

    public string GenerateInvitationToken()
    {
        using var rng = RandomNumberGenerator.Create();
        var tokenBytes = new byte[32];
        rng.GetBytes(tokenBytes);
        return Convert.ToBase64String(tokenBytes).Replace("+", "-").Replace("/", "_").Replace("=", "");
    }

    public async Task<string> StoreInvitationToken(string applicationId, string email, string token)
    {
        try
        {
            // CRITICAL: Normalize email for consistent token storage and email matching
            // Ensures tokens can be found regardless of email case variation
            var normalizedEmail = Utilities.NormalizeEmail(email);
            await InvalidatePreviousTokensForEmail(normalizedEmail, applicationId);

            var verificationCode = Utilities.GenerateVerificationCode();
            var tokenData = new InvitationTokenData
            {
                Email = normalizedEmail,
                ApplicationId = applicationId,
                Token = token,
                VerificationCode = verificationCode,
                CreatedUtc = DateTime.UtcNow,
                ExpiresUtc = DateTime.UtcNow.AddDays(_applicationOptions.InvitationTokenExpirationDays),
                Used = false,
                UsedUtc = null,
                IsSuperseded = false,
                SupersededUtc = null,
                SupersededReason = string.Empty
            };

            var containerClient = _blobServiceClient.GetBlobContainerClient(ContainerName);
            await containerClient.CreateIfNotExistsAsync(PublicAccessType.None);

            await UpdateTokenDataInBlob($"{token}.json", tokenData);
            return verificationCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error storing invitation token for {Email}", email);
            throw;
        }
    }

    public async Task<(bool IsValid, InvitationTokenData? TokenData, string ErrorMessage)> ValidateInvitationTokenOnly(string token)
    {
        try
        {
            var tokenData = await GetTokenDataFromBlob($"{token}.json");
            var (isValid, errorMessage) = ValidateTokenData(tokenData);
            return (isValid, isValid ? tokenData : null, errorMessage);
        }
        catch (Exception)
        {
            return (false, null, "Error validating invitation token");
        }
    }

    public async Task<(bool IsValid, InvitationTokenData? TokenData, string ErrorMessage)> ValidateInvitationByCode(string verificationCode)
    {
        try
        {
            var containerClient = _blobServiceClient.GetBlobContainerClient(ContainerName);

            // Search blob metadata only - much faster than downloading content
            await foreach (var blobItem in containerClient.GetBlobsAsync(BlobTraits.Metadata))
            {
                if (blobItem.Metadata?.TryGetValue("verificationCode", out var blobVerificationCode) == true &&
                    blobVerificationCode == verificationCode)
                {
                    // Found matching verification code - now get the actual token data
                    var tokenData = await GetTokenDataFromBlob(blobItem.Name);
                    if (tokenData != null)
                    {
                        var (isValid, errorMessage) = ValidateTokenData(tokenData, verificationCode);
                        return (isValid, isValid ? tokenData : null, errorMessage);
                    }
                }
            }

            return (false, null, "Invalid or expired invitation code");
        }
        catch (Exception)
        {
            return (false, null, "Error validating invitation code");
        }
    }

    public async Task<(bool IsValid, InvitationTokenData? TokenData, string ErrorMessage)> ValidateInvitationToken(string token, string verificationCode)
    {
        try
        {
            var tokenData = await GetTokenDataFromBlob($"{token}.json");
            var (isValid, errorMessage) = ValidateTokenData(tokenData, verificationCode);
            return (isValid, isValid ? tokenData : null, errorMessage);
        }
        catch (Exception)
        {
            return (false, null, "Error validating invitation token");
        }
    }

    public async Task<bool> MarkTokenAsUsed(string token)
    {
        try
        {
            var tokenData = await GetTokenDataFromBlob($"{token}.json");
            if (tokenData == null)
                return false;

            tokenData.Used = true;
            tokenData.UsedUtc = DateTime.UtcNow;

            return await UpdateTokenDataInBlob($"{token}.json", tokenData);
        }
        catch (Exception)
        {
            return false;
        }
    }

    public async Task InvalidatePreviousTokensForEmail(string email, string applicationId)
    {
        try
        {
            var containerClient = _blobServiceClient.GetBlobContainerClient(ContainerName);
            var tokensInvalidated = 0;

            await foreach (var blobItem in containerClient.GetBlobsAsync())
            {
                var tokenData = await GetTokenDataFromBlob(blobItem.Name);
                if (tokenData != null &&
                    tokenData.Email.Equals(email, StringComparison.OrdinalIgnoreCase) &&
                    tokenData.ApplicationId.Equals(applicationId, StringComparison.OrdinalIgnoreCase) &&
                    !tokenData.Used &&
                    !tokenData.IsSuperseded &&
                    DateTime.UtcNow <= tokenData.ExpiresUtc)
                {
                    tokenData.IsSuperseded = true;
                    tokenData.SupersededUtc = DateTime.UtcNow;
                    tokenData.SupersededReason = "Superseded by new invitation";

                    if (await UpdateTokenDataInBlob(blobItem.Name, tokenData))
                    {
                        tokensInvalidated++;
                    }
                }
            }

            
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error invalidating previous invitation tokens for {Email}", email);
            throw;
        }
    }

}
