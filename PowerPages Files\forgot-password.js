// Unified Notification System for Power Pages
const NotificationSystem = {
  config: { defaultTimeout: 5000, fadeInDuration: 300, fadeOutDuration: 300, autoHide: true, showIcons: true },
  icons: { success: 'fas fa-check-circle', error: 'fas fa-exclamation-circle', warning: 'fas fa-exclamation-triangle', info: 'fas fa-info-circle', loading: 'fas fa-spinner fa-spin' },

  show(type, message, options = {}) {
    const settings = { ...this.config, ...options };
    const container = this.getNotificationContainer(settings.containerId);
    if (!container) {
      console.warn('Notification container not found');
      return;
    }

    if (settings.clearExisting !== false) this.clear(container);

    const notification = this.createNotification(type, message, settings);
    container.appendChild(notification);
    notification.classList.add('notification-fade-in');

    if (settings.autoHide && settings.timeout > 0) {
      setTimeout(() => this.hide(notification), settings.timeout || this.config.defaultTimeout);
    }

    this.setAriaLiveRegion(notification, type);
    return notification;
  },

  showSuccess(message, options = {}) {
    return this.show('success', message, { ...options, timeout: options.timeout || 4000, title: options.title || 'Success' });
  },

  showError(message, options = {}) {
    return this.show('error', message, { ...options, timeout: options.timeout || 0, title: options.title || 'Error' });
  },

  showWarning(message, options = {}) {
    return this.show('warning', message, { ...options, timeout: options.timeout || 6000, title: options.title || 'Warning' });
  },

  showInfo(message, options = {}) {
    return this.show('info', message, { ...options, timeout: options.timeout || 5000, title: options.title || 'Information' });
  },

  showLoading(message, options = {}) {
    return this.show('loading', message, { ...options, timeout: 0, title: options.title || 'Loading', autoHide: false });
  },

  createNotification(type, message, settings) {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.setAttribute('role', type === 'error' ? 'alert' : 'status');
    notification.setAttribute('aria-live', type === 'error' ? 'assertive' : 'polite');

    let content = '';
    if (settings.showIcons && this.icons[type]) {
      content += `<div class="notification-icon"><i class="${this.icons[type]}" aria-hidden="true"></i></div>`;
    }

    content += '<div class="notification-content">';
    if (settings.title) {
      content += `<div class="notification-title">${this.escapeHtml(settings.title)}</div>`;
    }
    content += `<div class="notification-message">${this.escapeHtml(message)}</div></div>`;

    notification.innerHTML = content;
    if (settings.compact) notification.classList.add('notification-compact');
    return notification;
  },

  getNotificationContainer(containerId) {
    if (containerId) return document.getElementById(containerId);

    const commonIds = ['notificationContainer', 'messageContainer', 'alertContainer'];
    for (const id of commonIds) {
      const container = document.getElementById(id);
      if (container) return container;
    }

    const jquerySelectors = ['#errorMessage', '#successMessage', '#messageContent'];
    for (const selector of jquerySelectors) {
      const element = $(selector);
      if (element.length > 0) return element[0];
    }

    return this.createDefaultContainer();
  }

const SecureConfig = {
  getFunctionUrl: (functionName = 'PasswordService') => {
    const baseUrl = window.appConfig?.functionUrl;
    return baseUrl ? `${baseUrl}/api/${functionName}` : null;
  },

  getFunctionKey: () => {
    const functionKey = window.appConfig?.passwordFunctionKey;
    return (!functionKey || functionKey.includes('ERROR_MISSING')) ? null : functionKey;
  },

  buildSecureUrl: (functionName, operation) => {
    const baseUrl = SecureConfig.getFunctionUrl(functionName);
    const functionKey = SecureConfig.getFunctionKey();
    return (!baseUrl || !functionKey) ? null : `${baseUrl}?operation=${operation}&code=${functionKey}`;
  }
};

// Continue NotificationSystem methods
NotificationSystem.createDefaultContainer = () => {
  const container = document.createElement('div');
  container.id = 'notificationContainer';
  container.className = 'notification-container';

  const mainContent = document.querySelector('.card-body, .container, main, body');
  if (mainContent) {
    mainContent.insertBefore(container, mainContent.firstChild);
    return container;
  }
  return null;
};

NotificationSystem.hide = function(notification) {
  if (!notification || !notification.parentNode) return;
  notification.style.transition = `opacity ${this.config.fadeOutDuration}ms ease`;
  notification.style.opacity = '0';
  setTimeout(() => {
    if (notification.parentNode) notification.parentNode.removeChild(notification);
  }, this.config.fadeOutDuration);
};

NotificationSystem.clear = function(container) {
  if (!container) container = this.getNotificationContainer();
  if (container) {
    const notifications = container.querySelectorAll('.notification, .message, .alert');
    notifications.forEach(notification => this.hide(notification));
  }
  $('#errorMessage, #successMessage').hide();
};

NotificationSystem.setAriaLiveRegion = (notification, type) => {
  if (type === 'error') {
    notification.setAttribute('aria-live', 'assertive');
    notification.setAttribute('role', 'alert');
  } else {
    notification.setAttribute('aria-live', 'polite');
    notification.setAttribute('role', 'status');
  }
};

NotificationSystem.escapeHtml = text => {
  const div = document.createElement('div');
  div.textContent = text;
  return div.innerHTML;
};

// Consolidated error handling with preserved functionality
NotificationSystem.handleError = function(error, context = {}) {
  console.error('Error in context:', context, error);
  if (error.status) return this.handleHttpError(error, context);

  const errorTypes = {
    fetch: () => error instanceof TypeError && error.message.includes('fetch'),
    timeout: () => error.name === 'AbortError' || error.message.includes('timeout'),
    config: () => error.message.includes('configuration') || error.message.includes('missing'),
    rateLimit: () => error.message.includes('rate limit') || error.message.includes('429'),
    auth: () => error.message.includes('401') || error.message.includes('unauthorized')
  };

  const errorMessages = {
    fetch: { msg: 'Network connection failed. Please check your internet connection and try again.', title: 'Connection Error' },
    timeout: { msg: 'Request timed out. Please check your connection and try again. If the problem persists, try refreshing the page.', title: 'Request Timeout' },
    config: { msg: 'System configuration error. Please contact support if this persists.', title: 'Configuration Error' },
    rateLimit: { msg: error.message, title: 'Rate Limit', timeout: 10000 },
    auth: { msg: 'Authentication failed. Please refresh the page and try again.', title: 'Authentication Error' }
  };

  for (const [type, check] of Object.entries(errorTypes)) {
    if (check()) {
      const { msg, title, timeout = 0 } = errorMessages[type];
      return type === 'rateLimit' ? this.showWarning(msg, { title, timeout }) : this.showError(msg, { title, timeout });
    }
  }

  return this.showError(error.message || 'An unexpected error occurred. Please try again.', { title: 'Error', timeout: 0 });
};

NotificationSystem.handleHttpError = function(error, context = {}) {
  const httpErrors = {
    400: { msg: 'Invalid request. Please check your input and try again.', title: 'Invalid Request' },
    401: { msg: 'Authentication required. Please refresh the page and try again.', title: 'Authentication Required' },
    403: { msg: 'Access denied. You do not have permission to perform this action.', title: 'Access Denied' },
    404: { msg: 'Service not found. Please contact support if this persists.', title: 'Service Unavailable' },
    429: { msg: 'Too many requests. Please wait a moment before trying again.', title: 'Rate Limit Exceeded', timeout: 15000 },
    500: { msg: 'A server error occurred. Please try again later or contact support.', title: 'Server Error' }
  };

  const status = error.status;
  const errorInfo = httpErrors[status] || (status >= 500 ? httpErrors[500] : { msg: 'Request failed. Please try again or contact support if the problem persists.', title: 'Request Failed' });
  const { msg, title, timeout = 0 } = errorInfo;

  return status === 429 ? this.showWarning(msg, { title, timeout }) : this.showError(msg, { title, timeout });
};

NotificationSystem.validateConfiguration = function(requiredConfig = []) {
  const missing = requiredConfig.filter(key => !window.appConfig || !window.appConfig[key]);
  if (missing.length > 0) {
    this.showError(`Missing configuration: ${missing.join(', ')}. Please contact support.`, { title: 'Configuration Error', timeout: 0 });
    return false;
  }
  return true;
};

NotificationSystem.validateDOMElements = function(elementSelectors = []) {
  const missing = elementSelectors.filter(selector => !document.querySelector(selector));
  if (missing.length > 0) {
    console.error('Missing DOM elements:', missing);
    this.showError('Page elements missing. Please refresh the page.', { title: 'Page Error', timeout: 0 });
    return false;
  }
  return true;
};

NotificationSystem.checkBrowserCompatibility = function() {
  const features = [
    { check: () => typeof fetch === 'undefined', name: 'Fetch API' },
    { check: () => typeof Promise === 'undefined', name: 'Promise support' },
    { check: () => typeof URLSearchParams === 'undefined', name: 'URL Parameters' },
    { check: () => !document.querySelector || !document.querySelectorAll, name: 'Modern DOM methods' }
  ];

  try {
    if (typeof localStorage === 'undefined' || typeof sessionStorage === 'undefined') {
      features.push({ check: () => true, name: 'Local Storage' });
    }
  } catch (e) {
    features.push({ check: () => true, name: 'Local Storage' });
  }

  const missing = features.filter(f => f.check()).map(f => f.name);

  if (missing.length > 0) {
    const message = `Your browser is missing required features: ${missing.join(', ')}. Please update to a modern browser (Chrome 60+, Firefox 55+, Safari 12+, Edge 79+) to use this application.`;
    try {
      this.showError(message, { title: 'Browser Compatibility Issue', timeout: 0 });
    } catch (e) {
      alert('Browser Compatibility Issue: ' + message);
    }
    return false;
  }
  return true;
};

const PASSWORD_SERVICE_URL = SecureConfig.getFunctionUrl('PasswordService');
const APPLICATION_ID = window.appConfig?.applicationId || "default-application";
const APPLICATION_NAME = window.appConfig?.applicationName || document.getElementById('applicationName')?.value || "ApplicationNameNotSet";

if (!PASSWORD_SERVICE_URL) {
  console.error("PasswordService URL not configured");
  NotificationSystem.showError('System configuration missing. Please contact support.', { title: 'Configuration Error', timeout: 0 });
}

const InputSanitizer = {
  sanitizeInput: input => typeof input === 'string' ? input.trim().replace(/[<>"'&]/g, '').substring(0, 256) : '',
  sanitizeEmail: input => typeof input === 'string' ? input.trim().toLowerCase().replace(/[<>"'&]/g, '').substring(0, 256) : '',
  validateEmail: email => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email) && email.length <= 254;
  }
};

const errMsgDiv = $('#errorMessage');
const successMsgDiv = $('#successMessage');
const resetBtn = $('#resetButton');
const forgotPwdForm = $('#forgotPasswordForm');
const emailInput = $('#email');

// Unified notification functions
const showMessage = (message, isError = true, timeout = 0) => {
  const type = isError ? 'error' : 'success';
  const options = timeout > 0 ? { timeout } : {};
  return NotificationSystem.show(type, message, options);
};

const clearMessages = () => {
  NotificationSystem.clear();
  errMsgDiv.hide();
  successMsgDiv.hide();
};

const showSuccess = (message, timeout = 4000) => NotificationSystem.showSuccess(message, { timeout });
const showError = message => NotificationSystem.showError(message);
const showLoading = message => NotificationSystem.showLoading(message);

const validateEmail = () => {
  const email = emailInput.val().trim();
  const emailError = $('#emailError');

  emailInput.removeClass('is-invalid is-valid');
  emailInput.closest('.form-group').removeClass('has-error');

  if (!email) {
    emailError.text('Email address is required');
    emailInput.addClass('is-invalid');
    emailInput.closest('.form-group').addClass('has-error');
    emailInput.focus();
    return false;
  }

  if (!InputSanitizer.validateEmail(email)) {
    emailError.text('Please enter a valid email address');
    emailInput.addClass('is-invalid');
    emailInput.closest('.form-group').addClass('has-error');
    emailInput.focus();
    return false;
  }

  emailError.text('');
  emailInput.removeClass('is-invalid').addClass('is-valid');
  return true;
};

const validateForm = () => validateEmail();

const initiatePasswordReset = async email => {
  try {
    const sanitizedEmail = InputSanitizer.sanitizeEmail(email);

    if (!InputSanitizer.validateEmail(sanitizedEmail)) {
      throw new Error('Invalid email address');
    }

    const secureUrl = SecureConfig.buildSecureUrl('PasswordService', 'reset-initiate');
    if (!secureUrl) {
      throw new Error('Azure Function configuration missing. Please check Power Pages settings.');
    }

    const response = await fetch(secureUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Client-Version': '3.0.0-simplified',
        'X-Requested-With': 'XMLHttpRequest'
      },
      body: JSON.stringify({
        email: sanitizedEmail,
        applicationName: APPLICATION_NAME
      })
    });

    if (response.status === 429) {
      const responseText = await response.text();
      let result;
      try {
        result = JSON.parse(responseText);
        if (result.retryAfter) {
          const retryAfter = new Date(result.retryAfter);
          const waitTime = Math.ceil((retryAfter - new Date()) / 1000);
          throw new Error(`Too many requests. Please wait ${waitTime} seconds before trying again.`);
        }
      } catch (parseError) {
        throw new Error('Too many requests. Please wait a moment before trying again.');
      }
      throw new Error('Too many requests. Please wait a moment before trying again.');
    }

    const responseText = await response.text();
    let result;
    if (responseText) {
      try {
        result = JSON.parse(responseText);
      } catch (parseError) {
        throw new Error('Invalid response from server. Please try again.');
      }
    } else {
      throw new Error('Empty response from server. Please try again.');
    }

    return {
      success: true,
      message: result?.message || "If an account with that email exists, you will receive a reset link shortly."
    };

  } catch (error) {
    throw error;
  }
};

const initializeFormHandlers = () => {
  emailInput.blur(() => validateEmail());

  emailInput.on('input', function() {
    $(this).removeClass('is-invalid');
    $('#emailError').text('');
  });

  forgotPwdForm.submit(async function(event) {
    event.preventDefault();
    clearMessages();

    try {
      if (!validateForm()) return;

      resetBtn.prop("disabled", true);
      resetBtn.text("Sending...");

      showMessage("Sending reset link...", false);

      const email = emailInput.val().trim();
      const result = await initiatePasswordReset(email);

      if (result.success) {
        showMessage(result.message, false, true);
        forgotPwdForm[0].reset();
        emailInput.removeClass('is-invalid');
        $('#emailError').text('');
      }

    } catch (error) {
      console.error("Form submission error:", error);
      NotificationSystem.handleError(error, {
        operation: 'forgot-password',
        email: emailInput.val().trim()
      });
    } finally {
      resetBtn.prop("disabled", false);
      resetBtn.text("Send Reset Link");
    }
  });
};

$(document).ready(() => {
  if (!NotificationSystem.checkBrowserCompatibility()) return;

  const requiredConfig = ['functionUrl'];
  if (NotificationSystem.validateConfiguration(requiredConfig)) {
    const requiredElements = ['#forgotPasswordForm', '#email', '#resetButton'];
    if (NotificationSystem.validateDOMElements(requiredElements)) {
      initializeFormHandlers();
    }
  }
});
