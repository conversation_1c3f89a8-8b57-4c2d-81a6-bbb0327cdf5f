using Microsoft.Azure.Functions.Worker.Http;
using System.Security.Cryptography;

namespace PasswordHistoryValidator.Shared;

/// <summary>
/// Consolidated utility methods for common operations across the application.
/// </summary>
public static class Utilities
{
    /// <summary>
    /// Normalizes email addresses to lowercase for consistent processing.
    /// This prevents case sensitivity issues that could allow duplicate accounts
    /// with the same email address but different capitalization.
    /// </summary>
    /// <param name="email">The email address to normalize</param>
    /// <returns>Normalized email address in lowercase, or empty string if input is null/empty</returns>
    public static string NormalizeEmail(string? email)
    {
        return string.IsNullOrWhiteSpace(email) ? string.Empty : email.Trim().ToLowerInvariant();
    }

    /// <summary>
    /// Escapes single quotes in strings for OData queries to prevent injection attacks.
    /// </summary>
    /// <param name="value">The string value to escape</param>
    /// <returns>Escaped string safe for OData queries</returns>
    public static string EscapeODataString(string value)
    {
        return (value ?? string.Empty).Replace("'", "''");
    }

    /// <summary>
    /// Generates a cryptographically secure 6-digit verification code.
    /// </summary>
    /// <returns>6-digit verification code as string</returns>
    public static string GenerateVerificationCode()
    {
        using var rng = RandomNumberGenerator.Create();
        var bytes = new byte[4];
        rng.GetBytes(bytes);
        var randomNumber = BitConverter.ToUInt32(bytes, 0);
        var code = (randomNumber % 900000) + 100000; // 6 digit
        return code.ToString();
    }

    /// <summary>
    /// Generates a unique correlation ID for request tracking.
    /// </summary>
    /// <returns>Unique correlation ID as string</returns>
    public static string GenerateCorrelationId() => Guid.NewGuid().ToString();

    /// <summary>
    /// Extracts client identifier from HTTP request headers for rate limiting.
    /// Attempts to get real client IP from various proxy headers.
    /// </summary>
    /// <param name="req">HTTP request data</param>
    /// <returns>Client identifier string prefixed with "ip:"</returns>
    public static string GetClientIdentifier(HttpRequestData req)
    {
        var clientIp = req.Headers.GetValues("X-Forwarded-For").FirstOrDefault()?.Split(',').FirstOrDefault()?.Trim()
                      ?? req.Headers.GetValues("X-Real-IP").FirstOrDefault()
                      ?? req.Headers.GetValues("CF-Connecting-IP").FirstOrDefault()
                      ?? "unknown";

        return $"ip:{clientIp}";
    }
}
