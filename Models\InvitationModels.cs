using System.ComponentModel.DataAnnotations;

namespace PasswordHistoryValidator.Models;

/// <summary>
/// Request model for sending user invitations.
/// </summary>
public class UserInvitationRequest
{
    [Required(ErrorMessage = "Email is required")]
    [EmailAddress(ErrorMessage = "Invalid email format")]
    public string Email { get; set; } = string.Empty;
    
    [Required(ErrorMessage = "Application name is required")]
    public string ApplicationName { get; set; } = string.Empty;
    
    [Required(ErrorMessage = "First name is required")]
    public string FirstName { get; set; } = string.Empty;
    
    [Required(ErrorMessage = "Last name is required")]
    public string LastName { get; set; } = string.Empty;
    
    public string? Department { get; set; }
    public string? UserRole { get; set; }
    
    public string CorrelationId { get; set; } = Guid.NewGuid().ToString();
}
