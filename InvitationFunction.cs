using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Logging;
using System.Net;
using System.Text.Json;
using System.ComponentModel.DataAnnotations;
using PasswordHistoryValidator.Services;
using PasswordHistoryValidator.Shared;

namespace PasswordHistoryValidator;

public partial class InvitationFunction
{
    private readonly ILogger<InvitationFunction> _logger;
    private readonly InvitationTokenManager _invitationTokenManager;
    private readonly EmailService _emailService;
    private readonly RateLimitService _rateLimitService;
    private readonly JsonSerializerOptions _jsonOptions;

    public InvitationFunction(
        ILogger<InvitationFunction> logger,
        InvitationTokenManager invitationTokenManager,
        EmailService emailService,
        RateLimitService rateLimitService,
        JsonSerializerOptions jsonOptions)
    {
        _logger = logger;
        _invitationTokenManager = invitationTokenManager;
        _emailService = emailService;
        _rateLimitService = rateLimitService;
        _jsonOptions = jsonOptions;
    }

    /// send-invitation, validate-invitation

    [Function("InvitationService")]
    public async Task<HttpResponseData> Run(
        [HttpTrigger(AuthorizationLevel.Function, "post", "options")] HttpRequestData req,
        CancellationToken cancellationToken)
    {
        var correlationId = Utilities.GenerateCorrelationId();

        // Handle CORS preflight requests
        if (req.Method.Equals("OPTIONS", StringComparison.OrdinalIgnoreCase))
        {
            return HttpResponseHelper.CreateCorsResponse(req);
        }

        try
        {
            var operation = req.Query["operation"];
            if (string.IsNullOrEmpty(operation))
            {
                return await HttpResponseHelper.CreateErrorResponse(req, "Operation parameter required", correlationId, _jsonOptions);
            }

            return operation switch
            {
                "invite-user" => await HandleInviteUser(req, correlationId, cancellationToken),
                "validate-token" => await HandleValidateToken(req, correlationId, cancellationToken),
                _ => await HttpResponseHelper.CreateErrorResponse(req, $"Invalid operation: {operation}", correlationId, _jsonOptions)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Invitation service error [CorrelationId: {CorrelationId}]", correlationId);
            return await HttpResponseHelper.CreateErrorResponse(req, "Service error", correlationId, _jsonOptions);
        }
    }

    private async Task<HttpResponseData> HandleInviteUser(HttpRequestData req, string correlationId, CancellationToken cancellationToken)
    {
        InvitationRequest? data;
        try
        {
            data = await JsonSerializer.DeserializeAsync<InvitationRequest>(req.Body, _jsonOptions, cancellationToken);
        }
        catch (JsonException ex)
        {
            _logger.LogWarning("Invalid JSON in invitation request [CorrelationId: {CorrelationId}]: {Error}", correlationId, ex.Message);
            return await HttpResponseHelper.CreateErrorResponse(req, "Invalid JSON format in request", correlationId, _jsonOptions);
        }

        if (data == null)
        {
            _logger.LogWarning("Null request data in invitation request [CorrelationId: {CorrelationId}]", correlationId);
            return await HttpResponseHelper.CreateErrorResponse(req, "Invalid request data", correlationId, _jsonOptions);
        }

        // Validate request data
        var validationResults = new List<ValidationResult>();
        var validationContext = new ValidationContext(data);
        if (!Validator.TryValidateObject(data, validationContext, validationResults, true))
        {
            var errors = string.Join(", ", validationResults.Select(r => r.ErrorMessage));
            _logger.LogWarning("Validation failed for invitation request [CorrelationId: {CorrelationId}]: {Errors}", correlationId, errors);
            return await HttpResponseHelper.CreateErrorResponse(req, $"Validation failed: {errors}", correlationId, _jsonOptions);
        }

        // Rate limiting
        var rateLimitResponse = await HttpResponseHelper.CheckRateLimit(req, _rateLimitService, "invite-user", correlationId, _jsonOptions, cancellationToken);
        if (rateLimitResponse != null)
            return rateLimitResponse;

        // Generate and store invitation token
        var token = _invitationTokenManager.GenerateInvitationToken();
        var verificationCode = await _invitationTokenManager.StoreInvitationToken(data.ApplicationName, data.Email, token);

        // Send invitation email synchronously to ensure delivery
        await SendInvitationEmailAsync(data, token, verificationCode, correlationId);

        return await HttpResponseHelper.CreateJsonResponse(req, new
        {
            success = true,
            message = "Invitation sent successfully",
            correlationId = correlationId
        }, HttpStatusCode.OK, correlationId, _jsonOptions);
    }

    private async Task<HttpResponseData> HandleValidateToken(HttpRequestData req, string correlationId, CancellationToken cancellationToken)
    {
        var data = await JsonSerializer.DeserializeAsync<TokenValidationRequest>(req.Body, _jsonOptions, cancellationToken);

        if (data == null || string.IsNullOrEmpty(data.Token))
            return await HttpResponseHelper.CreateErrorResponse(req, "Token is required", correlationId, _jsonOptions);

        // Rate limiting
        var rateLimitResponse = await HttpResponseHelper.CheckRateLimit(req, _rateLimitService, "validate-token", correlationId, _jsonOptions, cancellationToken);
        if (rateLimitResponse != null)
            return rateLimitResponse;

        // Validate invitation token
        var (isValid, tokenData, errorMessage) = await _invitationTokenManager.ValidateInvitationTokenOnly(data.Token);

        if (!isValid || tokenData == null)
        {
            _logger.LogWarning("Token validation failed: {ErrorMessage} [CorrelationId: {CorrelationId}]",
                errorMessage, correlationId);
            return await HttpResponseHelper.CreateJsonResponse(req, new
            {
                success = false,
                message = errorMessage ?? "Invalid or expired invitation token"
            }, HttpStatusCode.Unauthorized, correlationId, _jsonOptions);
        }

        _logger.LogInformation("Token validation successful for {Email} [CorrelationId: {CorrelationId}]",
            tokenData.Email, correlationId);

        return await HttpResponseHelper.CreateJsonResponse(req, new
        {
            success = true,
            message = "Token is valid",
            email = tokenData.Email,
            applicationId = tokenData.ApplicationId,
            expiresUtc = tokenData.ExpiresUtc
        }, HttpStatusCode.OK, correlationId, _jsonOptions);
    }

    private async Task SendInvitationEmailAsync(InvitationRequest data, string token, string verificationCode, string correlationId)
    {
        try
        {
            var emailData = new InvitationEmailData(
                data.Email, token, verificationCode, data.ApplicationName,
                data.FirstName ?? "", data.LastName ?? "");
            var emailSent = await _emailService.SendUserInvitationEmailAsync(emailData, correlationId);

            if (emailSent)
            {
                _logger.LogInformation("Invitation email sent successfully to {Email} [CorrelationId: {CorrelationId}]",
                    data.Email, correlationId);
            }
            else
            {
                _logger.LogError("Failed to send invitation email to {Email} [CorrelationId: {CorrelationId}]",
                    data.Email, correlationId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending invitation email to {Email} [CorrelationId: {CorrelationId}]",
                data.Email, correlationId);
        }
    }

}
