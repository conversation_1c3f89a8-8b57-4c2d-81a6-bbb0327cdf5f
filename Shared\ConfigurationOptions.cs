namespace PasswordHistoryValidator.Shared;

/// <summary>
/// SendGrid email service configuration options.
/// </summary>
public class SendGridOptions
{
    public const string SectionName = "SendGrid";

    public string ApiKey { get; set; } = string.Empty;
    public string FromEmail { get; set; } = string.Empty;
    public string PasswordResetTemplateId { get; set; } = string.Empty;
    public string PasswordChangedTemplateId { get; set; } = string.Empty;
    public string UserInvitationTemplateId { get; set; } = string.Empty;
    public string AccountCreatedTemplateId { get; set; } = string.Empty;
    public string PasswordExpirationTemplateId { get; set; } = string.Empty;
    public string PasswordExpiredTemplateId { get; set; } = string.Empty;
}

/// <summary>
/// Entra External ID authentication configuration options.
/// </summary>
public class EntraExternalIDOptions
{
    public const string SectionName = "EntraExternalID";

    public string ClientId { get; set; } = string.Empty;
    public string ClientSecret { get; set; } = string.Empty;
    public string TenantId { get; set; } = string.Empty;
    public string DefaultDomain { get; set; } = string.Empty;
}

/// <summary>
/// Password history configuration options.
/// </summary>
public class PasswordHistoryOptions
{
    public const string SectionName = "PasswordHistory";

    public int MaxCount { get; set; } = 12;
    public int WorkFactor { get; set; } = 12;
}

/// <summary>
/// Rate limiting configuration options.
/// </summary>
public class RateLimitOptions
{
    public const string SectionName = "RateLimit";

    public int MaxRequestsPerMinute { get; set; } = 60;
}

/// <summary>
/// Password reset configuration options.
/// </summary>
public class PasswordResetOptions
{
    public const string SectionName = "PasswordReset";

    public string BaseUrl { get; set; } = string.Empty;
}

/// <summary>
/// Account registration configuration options.
/// </summary>
public class AccountRegistrationOptions
{
    public const string SectionName = "AccountRegistration";

    public string BaseUrl { get; set; } = string.Empty;
}

/// <summary>
/// Invitation system configuration options.
/// </summary>
public class InvitationOptions
{
    public const string SectionName = "Invitation";

    public int TokenExpirationDays { get; set; } = 45;
}

/// <summary>
/// General application configuration options.
/// These are typically configured as direct environment variables without sections.
/// </summary>
public class ApplicationOptions
{
    public string ApplicationName { get; set; } = string.Empty;
    public string KeyVaultUrl { get; set; } = string.Empty;
    public int PasswordExpirationDays { get; set; } = 90;
    public int PasswordWarningDays { get; set; } = 15;
    public int InvitationTokenExpirationDays { get; set; } = 45;
}

/// <summary>
/// Azure Storage configuration options.
/// Maintains separate class for storage due to special handling in Program.cs.
/// </summary>
public class StorageOptions
{
    public const string SectionName = "Storage";

    public string ConnectionString { get; set; } = string.Empty;
}
