using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Caching.Memory;
using System.Security.Cryptography;
using PasswordHistoryValidator.Shared;

namespace PasswordHistoryValidator.Services;

public class ResetTokenManager
{
    private readonly ILogger<ResetTokenManager> _logger;
    private readonly IMemoryCache _cache;
    private readonly EmailService _emailService;
    private const int TokenExpirationMinutes = 15;

    public ResetTokenManager(ILogger<ResetTokenManager> logger, IMemoryCache cache, EmailService emailService)
    {
        _logger = logger;
        _cache = cache;
        _emailService = emailService;
    }

    public string GenerateSecureToken()
    {
        using var rng = RandomNumberGenerator.Create();
        var tokenBytes = new byte[32];
        rng.GetBytes(tokenBytes);
        return Convert.ToBase64String(tokenBytes).Replace("+", "-").Replace("/", "_").Replace("=", "");
    }

    public string StoreResetToken(string applicationId, string email, string token)
    {
        // CRITICAL: Normalize email for consistent token storage and email matching
        // Ensures tokens can be found regardless of email case variation
        var normalizedEmail = Utilities.NormalizeEmail(email);
        var verificationCode = Utilities.GenerateVerificationCode();
        var tokenData = new ResetTokenData
        {
            Email = normalizedEmail,
            ApplicationId = applicationId,
            Token = token,
            VerificationCode = verificationCode,
            CreatedUtc = DateTime.UtcNow,
            ExpiresUtc = DateTime.UtcNow.AddMinutes(TokenExpirationMinutes),
            Used = false
        };

        _cache.Set($"reset_token_{token}", tokenData, TimeSpan.FromMinutes(TokenExpirationMinutes));
        return verificationCode;
    }

    public (bool IsValid, ResetTokenData? TokenData, string ErrorMessage) ValidateResetToken(string token, string verificationCode)
    {
        if (!_cache.TryGetValue($"reset_token_{token}", out ResetTokenData? tokenData) || tokenData == null)
            return (false, null, "Invalid or expired reset token");

        if (tokenData.Used)
            return (false, null, "Reset token has already been used");

        if (DateTime.UtcNow > tokenData.ExpiresUtc)
            return (false, null, "Reset token has expired");

        if (tokenData.VerificationCode != verificationCode)
            return (false, null, "Invalid verification code");

        return (true, tokenData, string.Empty);
    }

    public (bool IsValid, ResetTokenData? TokenData, string ErrorMessage) ValidateResetTokenOnly(string token)
    {
        try
        {
            if (!_cache.TryGetValue($"reset_token_{token}", out ResetTokenData? tokenData) || tokenData == null)
                return (false, null, "Invalid or expired reset token");

            if (tokenData.Used)
                return (false, null, "Reset token has already been used");

            if (DateTime.UtcNow > tokenData.ExpiresUtc)
                return (false, null, "Reset token has expired");

            return (true, tokenData, string.Empty);
        }
        catch (Exception)
        {
            return (false, null, "Error validating reset token");
        }
    }

    public bool MarkTokenAsUsed(string token)
    {
        var cacheKey = $"reset_token_{token}";
        if (!_cache.TryGetValue(cacheKey, out ResetTokenData? tokenData) || tokenData == null)
            return false;

        if (tokenData.Used)
            return true; // Already marked as used

        tokenData.Used = true;
        tokenData.UsedUtc = DateTime.UtcNow;
        _cache.Set(cacheKey, tokenData, TimeSpan.FromMinutes(TokenExpirationMinutes));
        return true;
    }

    public async Task SendResetEmail(string email, string token, string verificationCode, string applicationId, string correlationId)
    {
        try
        {
            var emailData = new PasswordResetEmailData(email, token, verificationCode);
            var emailSent = await _emailService.SendPasswordResetEmailAsync(emailData, correlationId);
            if (!emailSent)
                _logger.LogError("Failed to send password reset email to {Email}", email);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending reset email to {Email}", email);
        }
    }
}
