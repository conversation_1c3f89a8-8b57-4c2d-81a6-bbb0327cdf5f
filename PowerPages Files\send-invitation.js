// Unified Notification System for Power Pages
const NotificationSystem = {
  config: { defaultTimeout: 5000, fadeInDuration: 300, fadeOutDuration: 300, autoHide: true, showIcons: true },
  icons: { success: 'fas fa-check-circle', error: 'fas fa-exclamation-circle', warning: 'fas fa-exclamation-triangle', info: 'fas fa-info-circle', loading: 'fas fa-spinner fa-spin' },

  show(type, message, options = {}) {
    const settings = { ...this.config, ...options };
    const container = this.getNotificationContainer(settings.containerId);
    if (!container) {
      console.warn('Notification container not found');
      return;
    }

    if (settings.clearExisting !== false) this.clear(container);

    const notification = this.createNotification(type, message, settings);
    container.appendChild(notification);
    notification.classList.add('notification-fade-in');

    if (settings.autoHide && settings.timeout > 0) {
      setTimeout(() => this.hide(notification), settings.timeout || this.config.defaultTimeout);
    }

    this.setAriaLiveRegion(notification, type);
    return notification;
  },

  showSuccess(message, options = {}) {
    return this.show('success', message, { ...options, timeout: options.timeout || 4000, title: options.title || 'Success' });
  },

  showError(message, options = {}) {
    return this.show('error', message, { ...options, timeout: options.timeout || 0, title: options.title || 'Error' });
  },

  showWarning(message, options = {}) {
    return this.show('warning', message, { ...options, timeout: options.timeout || 6000, title: options.title || 'Warning' });
  },

  showInfo(message, options = {}) {
    return this.show('info', message, { ...options, timeout: options.timeout || 5000, title: options.title || 'Information' });
  },

  showLoading(message, options = {}) {
    return this.show('loading', message, { ...options, timeout: 0, title: options.title || 'Loading', autoHide: false });
  },

  createNotification(type, message, settings) {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.setAttribute('role', type === 'error' ? 'alert' : 'status');
    notification.setAttribute('aria-live', type === 'error' ? 'assertive' : 'polite');

    let content = '';
    if (settings.showIcons && this.icons[type]) {
      content += `<div class="notification-icon"><i class="${this.icons[type]}" aria-hidden="true"></i></div>`;
    }

    content += '<div class="notification-content">';
    if (settings.title) {
      content += `<div class="notification-title">${this.escapeHtml(settings.title)}</div>`;
    }
    content += `<div class="notification-message">${this.escapeHtml(message)}</div></div>`;

    notification.innerHTML = content;
    if (settings.compact) notification.classList.add('notification-compact');
    return notification;
  },

  getNotificationContainer(containerId) {
    if (containerId) return document.getElementById(containerId);

    const commonIds = ['notificationContainer', 'messageContainer', 'alertContainer'];
    for (const id of commonIds) {
      const container = document.getElementById(id);
      if (container) return container;
    }

    const jquerySelectors = ['#errorMessage', '#successMessage', '#messageContent'];
    for (const selector of jquerySelectors) {
      const element = $(selector);
      if (element.length > 0) return element[0];
    }

    return this.createDefaultContainer();
  },

  createDefaultContainer() {
    const container = document.createElement('div');
    container.id = 'notificationContainer';
    container.className = 'notification-container';

    const mainContent = document.querySelector('.card-body, .container, main, body');
    if (mainContent) {
      mainContent.insertBefore(container, mainContent.firstChild);
      return container;
    }
    return null;
  },

  hide(notification) {
    if (!notification || !notification.parentNode) return;
    notification.style.transition = `opacity ${this.config.fadeOutDuration}ms ease`;
    notification.style.opacity = '0';
    setTimeout(() => {
      if (notification.parentNode) notification.parentNode.removeChild(notification);
    }, this.config.fadeOutDuration);
  },

  clear(container) {
    if (!container) container = this.getNotificationContainer();
    if (container) {
      const notifications = container.querySelectorAll('.notification, .message, .alert');
      notifications.forEach(notification => this.hide(notification));
    }
    $('#errorMessage, #successMessage').hide();
  },

  setAriaLiveRegion(notification, type) {
    if (type === 'error') {
      notification.setAttribute('aria-live', 'assertive');
      notification.setAttribute('role', 'alert');
    } else {
      notification.setAttribute('aria-live', 'polite');
      notification.setAttribute('role', 'status');
    }
  },

  escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  },

  // Consolidated error handling with preserved functionality
  handleError(error, context = {}) {
    console.error('Error in context:', context, error);
    if (error.status) return this.handleHttpError(error, context);

    const errorTypes = {
      fetch: () => error instanceof TypeError && error.message.includes('fetch'),
      timeout: () => error.name === 'AbortError' || error.message.includes('timeout'),
      config: () => error.message.includes('configuration') || error.message.includes('missing'),
      rateLimit: () => error.message.includes('rate limit') || error.message.includes('429'),
      auth: () => error.message.includes('401') || error.message.includes('unauthorized')
    };

    const errorMessages = {
      fetch: { msg: 'Network connection failed. Please check your internet connection and try again.', title: 'Connection Error' },
      timeout: { msg: 'Request timed out. Please check your connection and try again. If the problem persists, try refreshing the page.', title: 'Request Timeout' },
      config: { msg: 'System configuration error. Please contact support if this persists.', title: 'Configuration Error' },
      rateLimit: { msg: error.message, title: 'Rate Limit', timeout: 10000 },
      auth: { msg: 'Authentication failed. Please refresh the page and try again.', title: 'Authentication Error' }
    };

    for (const [type, check] of Object.entries(errorTypes)) {
      if (check()) {
        const { msg, title, timeout = 0 } = errorMessages[type];
        return type === 'rateLimit' ? this.showWarning(msg, { title, timeout }) : this.showError(msg, { title, timeout });
      }
    }

    return this.showError(error.message || 'An unexpected error occurred. Please try again.', { title: 'Error', timeout: 0 });
  },

  handleHttpError(error, context = {}) {
    const httpErrors = {
      400: { msg: 'Invalid request. Please check your input and try again.', title: 'Invalid Request' },
      401: { msg: 'Authentication required. Please refresh the page and try again.', title: 'Authentication Required' },
      403: { msg: 'Access denied. You do not have permission to perform this action.', title: 'Access Denied' },
      404: { msg: 'Service not found. Please contact support if this persists.', title: 'Service Unavailable' },
      429: { msg: 'Too many requests. Please wait a moment before trying again.', title: 'Rate Limit Exceeded', timeout: 15000 },
      500: { msg: 'A server error occurred. Please try again later or contact support.', title: 'Server Error' }
    };

    const status = error.status;

    if (status === 409) {
      const conflictMsg = error.message || 'A conflict occurred. Please review your input.';
      if (conflictMsg.toLowerCase().includes('email') &&
          (conflictMsg.toLowerCase().includes('exists') || conflictMsg.toLowerCase().includes('already') || conflictMsg.toLowerCase().includes('duplicate'))) {
        return this.showError('An account with this email address already exists. Please try signing in instead.', { title: 'Account Already Exists', timeout: 0 });
      }
      return this.showError(conflictMsg, { title: 'Conflict', timeout: 0 });
    }

    const errorInfo = httpErrors[status] || (status >= 500 ? httpErrors[500] : { msg: 'Request failed. Please try again or contact support if the problem persists.', title: 'Request Failed' });
    const { msg, title, timeout = 0 } = errorInfo;

    return status === 429 ? this.showWarning(msg, { title, timeout }) : this.showError(msg, { title, timeout });
  },

  validateConfiguration(requiredConfig = []) {
    const missing = requiredConfig.filter(key => !window.appConfig || !window.appConfig[key]);
    if (missing.length > 0) {
      this.showError(`Missing configuration: ${missing.join(', ')}. Please contact support.`, { title: 'Configuration Error', timeout: 0 });
      return false;
    }
    return true;
  },

  validateDOMElements(elementSelectors = []) {
    const missing = elementSelectors.filter(selector => !document.querySelector(selector));
    if (missing.length > 0) {
      console.error('Missing DOM elements:', missing);
      this.showError('Page elements missing. Please refresh the page.', { title: 'Page Error', timeout: 0 });
      return false;
    }
    return true;
  },

  checkBrowserCompatibility() {
    const features = [
      { check: () => typeof fetch === 'undefined', name: 'Fetch API' },
      { check: () => typeof Promise === 'undefined', name: 'Promise support' },
      { check: () => typeof URLSearchParams === 'undefined', name: 'URL Parameters' },
      { check: () => !document.querySelector || !document.querySelectorAll, name: 'Modern DOM methods' }
    ];

    try {
      if (typeof localStorage === 'undefined' || typeof sessionStorage === 'undefined') {
        features.push({ check: () => true, name: 'Local Storage' });
      }
    } catch (e) {
      features.push({ check: () => true, name: 'Local Storage' });
    }

    const missing = features.filter(f => f.check()).map(f => f.name);

    if (missing.length > 0) {
      const message = `Your browser is missing required features: ${missing.join(', ')}. Please update to a modern browser (Chrome 60+, Firefox 55+, Safari 12+, Edge 79+) to use this application.`;
      try {
        this.showError(message, { title: 'Browser Compatibility Issue', timeout: 0 });
      } catch (e) {
        alert('Browser Compatibility Issue: ' + message);
      }
      return false;
    }
    return true;
  }
};

const SecureConfig = {
  getFunctionUrl(functionName = 'InvitationService') {
    const baseUrl = window.appConfig?.functionUrl;
    if (!baseUrl || baseUrl.includes('ERROR_MISSING')) return null;
    return `${baseUrl}/api/${functionName}`;
  },

  getFunctionKey() {
    const functionKey = window.appConfig?.invitationFunctionKey;
    if (!functionKey || functionKey.includes('ERROR_MISSING')) return null;
    return functionKey;
  },

  buildSecureUrl(functionName, operation) {
    const baseUrl = this.getFunctionUrl(functionName);
    const functionKey = this.getFunctionKey();
    if (!baseUrl || !functionKey) return null;
    return `${baseUrl}?operation=${operation}&code=${functionKey}`;
  }
};

let currentInvitationData = null;

const initializeConfiguration = () => {
  const config = window.appConfig;
  const missing = [];

  if (!config?.functionUrl || config.functionUrl.includes('ERROR_MISSING')) missing.push('Azure Function URL');
  if (!config?.applicationName || config.applicationName.includes('ERROR_MISSING')) missing.push('Application Name');
  if (!config?.invitationFunctionKey || config.invitationFunctionKey.includes('ERROR_MISSING')) missing.push('Invitation Function Key');

  if (missing.length > 0) {
    showMessage(`Configuration error: Missing ${missing.join(', ')}. Please check Power Pages settings.`, true);
  }
};

const InputSanitizer = {
  sanitizeString: input => typeof input === 'string' ? input.trim().replace(/[<>"'&]/g, '').substring(0, 256) : '',
  sanitizeEmail: input => typeof input === 'string' ? input.trim().toLowerCase().replace(/[<>"'&]/g, '').substring(0, 256) : '',

  validateEmail: email => {
    if (!email || typeof email !== 'string') return false;
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email.trim());
  },

  validateName: name => {
    if (!name || typeof name !== 'string') return false;
    const trimmed = name.trim();
    if (trimmed.length < 1 || trimmed.length > 50) return false;

    const namePattern = /^[a-zA-ZÀ-ÿĀ-žА-я\u4e00-\u9fff\s'-]+$/;
    if (!namePattern.test(trimmed)) return false;
    if (!/[a-zA-ZÀ-ÿĀ-žА-я\u4e00-\u9fff]/.test(trimmed)) return false;
    if (/[-']{3,}/.test(trimmed)) return false;
    if (/^[-'\s]|[-'\s]$/.test(trimmed)) return false;

    return true;
  },

  validateNameWithFeedback: (name, fieldName) => {
    if (!name || typeof name !== 'string') return { valid: false, message: `${fieldName} is required` };

    const trimmed = name.trim();
    if (trimmed.length < 1) return { valid: false, message: `${fieldName} is required` };
    if (trimmed.length > 50) return { valid: false, message: `${fieldName} must be 50 characters or less` };

    const namePattern = /^[a-zA-ZÀ-ÿĀ-žА-я\u4e00-\u9fff\s'-]+$/;
    if (!namePattern.test(trimmed)) return { valid: false, message: `${fieldName} can only contain letters, spaces, hyphens, and apostrophes` };
    if (!/[a-zA-ZÀ-ÿĀ-žА-я\u4e00-\u9fff]/.test(trimmed)) return { valid: false, message: `${fieldName} must contain at least one letter` };
    if (/[-']{3,}/.test(trimmed)) return { valid: false, message: `${fieldName} cannot contain more than 2 consecutive special characters` };
    if (/^[-'\s]|[-'\s]$/.test(trimmed)) return { valid: false, message: `${fieldName} cannot start or end with special characters` };

    return { valid: true, message: '' };
  }
};

const errMsg = $('#errorMessage');
const successMsg = $('#successMessage');
const invitationForm = $('#invitationForm');
const sendBtn = $('#sendButton');
const recentInvitations = $('#recentInvitations');

// Unified notification functions
const showMessage = (message, isError = true) => {
  const type = isError ? 'error' : 'success';
  NotificationSystem.show(type, message);

  errMsg.addClass('d-none');
  successMsg.addClass('d-none');

  if (isError) {
    $('#errorText').text(message);
    errMsg.removeClass('d-none');
  } else {
    $('#successText').text(message);
    successMsg.removeClass('d-none');
  }
};

const showSuccess = (message, timeout = 4000) => NotificationSystem.showSuccess(message, { timeout });
const showError = message => NotificationSystem.showError(message);
const showLoading = message => NotificationSystem.showLoading(message);

const clearMessages = () => {
  NotificationSystem.clear();
  errMsg.addClass('d-none');
  successMsg.addClass('d-none');
};

const showLoadingState = message => {
  sendBtn.prop('disabled', true).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> ' + message);
};

const resetLoadingState = () => {
  sendBtn.prop('disabled', false).html('<i class="fas fa-paper-plane me-2"></i>Send Invitation');
};

async function sendInvitation(invitationData) {
  showLoadingState('Sending Invitation...');

  try {
    const secureUrl = SecureConfig.buildSecureUrl('InvitationService', 'invite-user');
    if (!secureUrl) {
      throw new Error('Azure Function configuration missing. Please check Power Pages settings.');
    }

    // Validate required configuration
    const applicationName = window.appConfig?.applicationName;
    if (!applicationName || applicationName.includes('ERROR_MISSING')) {
      throw new Error('Application name configuration missing. Please check Power Pages settings.');
    }

    const requestBody = {
      Email: InputSanitizer.sanitizeEmail(invitationData.email),
      FirstName: InputSanitizer.sanitizeString(invitationData.firstName),
      LastName: InputSanitizer.sanitizeString(invitationData.lastName),
      ApplicationName: applicationName
    };

    const response = await fetch(secureUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Client-Version': '3.0.0-simplified',
        'X-Requested-With': 'XMLHttpRequest'
      },
      body: JSON.stringify(requestBody)
    });

    if (response.status === 429) {
      throw new Error('Too many requests. Please wait a moment before trying again.');
    }

    if (!response.ok) {
      let errorDetails = 'No additional details';
      try {
        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
          const errorData = await response.json();
          errorDetails = errorData.message || errorData.error || JSON.stringify(errorData);
        } else {
          const textResponse = await response.text();
          errorDetails = textResponse || 'Empty response';
        }
      } catch (parseError) {
      }
      throw new Error(`Server error: ${response.status} - ${errorDetails}`);
    }

    const contentType = response.headers.get('content-type');
    if (!contentType || !contentType.includes('application/json')) {
      throw new Error(`Server error: ${response.status} - Expected JSON response`);
    }

    const result = await response.json();

    let success, message, errorCode, retryAfter;

    if (result.data) {
      success = result.data.success;
      message = result.data.message;
      errorCode = result.data.errorCode;
      retryAfter = result.data.retryAfter;
    } else {
      success = result.success;
      message = result.message;
      errorCode = result.errorCode;
      retryAfter = result.retryAfter;
    }

    const isSuccess = response.ok && success === true;

    if (!isSuccess) {
      if (response.status === 429 || errorCode === 'RateLimitExceeded') {
        const retryMessage = retryAfter ? ` Please try again after ${new Date(retryAfter).toLocaleTimeString()}.` : ' Please try again later.';
        throw new Error((message || 'Rate limit exceeded.') + retryMessage);
      }
      throw new Error(message || 'Failed to send invitation');
    }

    return {
      success: true,
      message: message || 'Invitation sent successfully',
      correlationId: result.correlationId
    };
  } catch (error) {
    throw error;
  } finally {
    resetLoadingState();
  }
}

function validateForm() {
  let isValid = true;

  // Clear previous validation states
  $('.form-control').removeClass('is-invalid is-valid');
  $('.invalid-feedback').text('');
  $('.form-group').removeClass('has-error');

  // Email validation
  const email = $('#email').val();
  const emailValid = validateFieldWithVisualFeedback('#email', '#emailError',
    () => InputSanitizer.validateEmail(email),
    'Please enter a valid email address'
  );
  if (!emailValid) isValid = false;

  // First name validation
  const firstName = $('#firstName').val();
  const firstNameValidation = InputSanitizer.validateNameWithFeedback(firstName, 'First name');
  const firstNameValid = validateFieldWithVisualFeedback('#firstName', '#firstNameError',
    () => firstNameValidation.valid,
    firstNameValidation.message
  );
  if (!firstNameValid) isValid = false;

  // Last name validation
  const lastName = $('#lastName').val();
  const lastNameValidation = InputSanitizer.validateNameWithFeedback(lastName, 'Last name');
  const lastNameValid = validateFieldWithVisualFeedback('#lastName', '#lastNameError',
    () => lastNameValidation.valid,
    lastNameValidation.message
  );
  if (!lastNameValid) isValid = false;

  // Show summary notification if validation fails
  if (!isValid) {
    NotificationSystem.showError('Please correct the highlighted fields and try again.', {
      title: 'Form Validation',
      timeout: 5000
    });
  }

  return isValid;
}

const validateFieldWithVisualFeedback = (fieldSelector, errorSelector, validationFunction, errorMessage) => {
  const field = $(fieldSelector);
  const errorElement = $(errorSelector);
  const isValid = validationFunction();

  if (isValid) {
    field.removeClass('is-invalid').addClass('is-valid');
    field.closest('.form-group').removeClass('has-error');
    errorElement.text('');
    return true;
  } else {
    field.removeClass('is-valid').addClass('is-invalid');
    field.closest('.form-group').addClass('has-error');
    errorElement.text(errorMessage);

    if ($('.is-invalid').length === 1) field.focus();
    return false;
  }
};

const addToRecentInvitations = (email, firstName, lastName) => {
  const timestamp = new Date().toLocaleString();
  const invitationHtml = `
    <div class="d-flex justify-content-between align-items-center py-1">
      <span><strong>${firstName} ${lastName}</strong> (${email})</span>
      <small class="text-muted">${timestamp}</small>
    </div>
  `;

  if (recentInvitations.text().includes('No recent invitations')) {
    recentInvitations.html(invitationHtml);
  } else {
    recentInvitations.prepend(invitationHtml);
  }

  const invitations = recentInvitations.children();
  if (invitations.length > 5) invitations.slice(5).remove();
};

const initializeFormHandlers = () => {
  invitationForm.submit(async function(event) {
    event.preventDefault();
    showMessage('', false);

    try {
      if (!validateForm()) return;

      const invitationData = {
        email: $('#email').val(),
        firstName: $('#firstName').val(),
        lastName: $('#lastName').val()
      };

      currentInvitationData = invitationData;
      const result = await sendInvitation(invitationData);

      if (result.success) {
        showMessage(`Invitation sent successfully to ${invitationData.email}!`, false);
        addToRecentInvitations(invitationData.email, invitationData.firstName, invitationData.lastName);
        invitationForm[0].reset();
        currentInvitationData = null;
      }

    } catch (error) {
      NotificationSystem.handleError(error, {
        operation: 'send-invitation',
        email: $('#email').val()
      });
    }
  });
};

$(document).ready(() => {
  if (!NotificationSystem.checkBrowserCompatibility()) return;
  initializeConfiguration();
  initializeFormHandlers();
});
