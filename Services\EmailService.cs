using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using SendGrid;
using SendGrid.Helpers.Mail;
using PasswordHistoryValidator.Shared;

namespace PasswordHistoryValidator.Services
{

    public record PasswordResetEmailData(string ToEmail, string ResetToken, string VerificationCode);
    public record InvitationEmailData(string ToEmail, string InvitationToken, string VerificationCode, string ApplicationName, string FirstName, string LastName);
    public record AccountCreatedEmailData(string ToEmail, string FirstName, string ApplicationName);
    public record PasswordExpirationEmailData(string ToEmail, string ApplicationName, int DaysUntilExpiration, bool IsAbsent = false);
    public record PasswordExpiredEmailData(string ToEmail, string ApplicationName);

    public class EmailService
    {
        private readonly ILogger<EmailService> _logger;
        private readonly ISendGridClient _sendGridClient;
        private readonly string _fromEmail;
        private readonly string _resetBaseUrl;
        private readonly string _registrationBaseUrl;
        private readonly string _applicationName;
        private readonly string _fromName;
        private readonly SendGridOptions _sendGridOptions;

        public EmailService(ILogger<EmailService> logger, IOptions<SendGridOptions> sendGridOptions, IOptions<ApplicationOptions> applicationOptions, IOptions<PasswordResetOptions> passwordResetOptions, IOptions<AccountRegistrationOptions> accountRegistrationOptions)
        {
            _logger = logger;
            _sendGridOptions = sendGridOptions.Value;
            var appOpts = applicationOptions.Value;
            var passwordResetOpts = passwordResetOptions.Value;
            var accountRegistrationOpts = accountRegistrationOptions.Value;


            if (string.IsNullOrEmpty(_sendGridOptions.ApiKey))
            {
                throw new InvalidOperationException("SendGrid:ApiKey is required but not configured. Configure your SendGrid API key in application settings or Azure Key Vault.");
            }

            _sendGridClient = new SendGridClient(_sendGridOptions.ApiKey);


            _fromEmail = _sendGridOptions.FromEmail ?? throw new InvalidOperationException("SendGrid:FromEmail is required but not configured.");
            _resetBaseUrl = passwordResetOpts.BaseUrl ?? throw new InvalidOperationException("PasswordReset:BaseUrl is required but not configured.");
            _registrationBaseUrl = accountRegistrationOpts.BaseUrl ?? throw new InvalidOperationException("AccountRegistration:BaseUrl is required but not configured.");


            _applicationName = "Password Reset Service";
            _fromName = "Password Reset Service";
        }




        private string ValidateTemplateIdRequired(string? templateId, string configurationKey)
        {
            if (string.IsNullOrEmpty(templateId))
            {
                throw new InvalidOperationException($"{configurationKey} is required but not configured.");
            }
            return templateId;
        }

        private async Task<bool> SendEmailWithTemplate(string toEmail, string templateId, object templateData, string emailType, string correlationId, CancellationToken cancellationToken = default)
        {
            try
            {
                var from = new EmailAddress(_fromEmail, _fromName);
                var to = new EmailAddress(toEmail);

                var message = new SendGridMessage()
                {
                    From = from,
                    TemplateId = templateId
                };
                message.AddTo(to);
                message.SetTemplateData(templateData);
                message.AddCustomArg("X-Correlation-ID", correlationId);
                message.AddCustomArg("X-Email-Type", emailType);
                message.AddCustomArg("X-Application-Name", _applicationName);

                var response = await _sendGridClient.SendEmailAsync(message, cancellationToken);

                if (response.IsSuccessStatusCode)
                {
                    return true;
                }
                else
                {
                    var errorMessage = response.Body != null ? await response.Body.ReadAsStringAsync() : "Unknown error";
                    _logger.LogError("Failed to send {EmailType} email to {Email}. StatusCode: {StatusCode}, Error: {Error} [CorrelationId: {CorrelationId}]",
                        emailType, toEmail, response.StatusCode, errorMessage, correlationId);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending {EmailType} email to {Email} [CorrelationId: {CorrelationId}]", emailType, toEmail, correlationId);
                return false;
            }
        }

        public async Task<bool> SendPasswordResetEmailAsync(PasswordResetEmailData data, string correlationId, CancellationToken cancellationToken = default)
        {
            var templateId = ValidateTemplateIdRequired(_sendGridOptions.PasswordResetTemplateId, "SendGrid:PasswordResetTemplateId");

            var resetLink = $"{_resetBaseUrl}?token={data.ResetToken}";
            var templateData = new
            {
                resetLink = resetLink,
                verificationCode = data.VerificationCode,
                applicationName = _applicationName
            };

            return await SendEmailWithTemplate(data.ToEmail, templateId, templateData, "passwordReset", correlationId, cancellationToken);
        }

        public async Task<bool> SendPasswordChangedNotificationAsync(string toEmail, string correlationId, CancellationToken cancellationToken = default)
        {
            var templateId = ValidateTemplateIdRequired(_sendGridOptions.PasswordChangedTemplateId, "SendGrid:PasswordChangedTemplateId");

            var templateData = new
            {
                applicationName = _applicationName,
                email = toEmail,
                changeDate = DateTime.UtcNow.ToString("yyyy-MM-dd"),
                changeTime = DateTime.UtcNow.ToString("HH:mm"),
                correlationId = correlationId
            };

            return await SendEmailWithTemplate(toEmail, templateId, templateData, "passwordChanged", correlationId, cancellationToken);
        }

        public async Task<bool> SendUserInvitationEmailAsync(InvitationEmailData data, string correlationId, CancellationToken cancellationToken = default)
        {
            var templateId = ValidateTemplateIdRequired(_sendGridOptions.UserInvitationTemplateId, "SendGrid:UserInvitationTemplateId");

            var baseUrl = _registrationBaseUrl.TrimEnd('/');
            var invitationLink = $"{baseUrl}?token={data.InvitationToken}";

            var templateData = new
            {
                invitationLink = invitationLink,
                verificationCode = data.VerificationCode,
                applicationName = data.ApplicationName,
                firstName = data.FirstName,
                lastName = data.LastName,
                fullName = $"{data.FirstName} {data.LastName}"
            };

            return await SendEmailWithTemplate(data.ToEmail, templateId, templateData, "invitation", correlationId, cancellationToken);
        }


        private string GetWebsiteUrl()
        {
            return _resetBaseUrl.Replace("/Custom-Password-Reset/", "").TrimEnd('/');
        }

        public async Task<bool> SendAccountCreatedNotificationAsync(AccountCreatedEmailData data, string correlationId, CancellationToken cancellationToken = default)
        {
            var templateId = ValidateTemplateIdRequired(_sendGridOptions.AccountCreatedTemplateId, "SendGrid:AccountCreatedTemplateId");

            var templateData = new
            {
                firstName = data.FirstName,
                applicationName = data.ApplicationName,
                email = data.ToEmail,
                creationDate = DateTime.UtcNow.ToString("yyyy-MM-dd"),
                creationTime = DateTime.UtcNow.ToString("HH:mm"),
                correlationId = correlationId
            };

            return await SendEmailWithTemplate(data.ToEmail, templateId, templateData, "accountCreated", correlationId, cancellationToken);
        }

        public async Task<bool> SendPasswordExpirationNotificationAsync(PasswordExpirationEmailData data, string correlationId, CancellationToken cancellationToken = default)
        {
            var templateId = ValidateTemplateIdRequired(_sendGridOptions.PasswordExpirationTemplateId, "SendGrid:PasswordExpirationTemplateId");

            var websiteUrl = GetWebsiteUrl();
            var forgotPasswordUrl = $"{websiteUrl}/Custom-Password-Reset/";

            var templateData = new
            {
                applicationName = data.ApplicationName,
                daysUntilExpiration = data.DaysUntilExpiration,
                websiteUrl = websiteUrl,
                forgotPasswordUrl = forgotPasswordUrl,
                email = data.ToEmail,
                notificationDate = DateTime.UtcNow.ToString("yyyy-MM-dd"),
                correlationId = correlationId,
                absenceWarning = data.IsAbsent // Flag for template to show absence-specific messaging
            };

            return await SendEmailWithTemplate(data.ToEmail, templateId, templateData, "passwordExpiration", correlationId, cancellationToken);
        }

        public async Task<bool> SendPasswordExpiredNotificationAsync(PasswordExpiredEmailData data, string correlationId, CancellationToken cancellationToken = default)
        {
            var templateId = ValidateTemplateIdRequired(_sendGridOptions.PasswordExpiredTemplateId, "SendGrid:PasswordExpiredTemplateId");

            var websiteUrl = GetWebsiteUrl();
            var forgotPasswordUrl = $"{websiteUrl}/Custom-Password-Reset/";

            var templateData = new
            {
                applicationName = data.ApplicationName,
                websiteUrl = websiteUrl,
                forgotPasswordUrl = forgotPasswordUrl,
                email = data.ToEmail,
                expiredDate = DateTime.UtcNow.ToString("yyyy-MM-dd"),
                correlationId = correlationId
            };

            return await SendEmailWithTemplate(data.ToEmail, templateId, templateData, "passwordExpired", correlationId, cancellationToken);
        }
    }
}
