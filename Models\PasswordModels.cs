using System.ComponentModel.DataAnnotations;

namespace PasswordHistoryValidator.Models;

/// <summary>
/// Request model for password validation operations.
/// Used to check if a password meets history requirements.
/// </summary>
public class PasswordValidationRequest
{
    [Required(ErrorMessage = "Email is required")]
    [EmailAddress(ErrorMessage = "Invalid email format")]
    public string Email { get; set; } = string.Empty;
    
    [Required(ErrorMessage = "Password is required")]
    [MinLength(8, ErrorMessage = "Password must be at least 8 characters")]
    public string Password { get; set; } = string.Empty;
    
    [Required(ErrorMessage = "Application name is required")]
    public string ApplicationName { get; set; } = string.Empty;
    
    public string? UserId { get; set; }
    
    public string CorrelationId { get; set; } = Guid.NewGuid().ToString();
}

/// <summary>
/// Request model for initiating password reset process.
/// </summary>
public class PasswordResetRequest
{
    [Required(ErrorMessage = "Email is required")]
    [EmailAddress(ErrorMessage = "Invalid email format")]
    public string Email { get; set; } = string.Empty;
    
    [Required(ErrorMessage = "Application name is required")]
    public string ApplicationName { get; set; } = string.Empty;
    
    public string CorrelationId { get; set; } = Guid.NewGuid().ToString();
}

/// <summary>
/// Request model for completing password reset with new password.
/// </summary>
public class PasswordResetCompleteRequest
{
    [Required(ErrorMessage = "Token is required")]
    public string Token { get; set; } = string.Empty;
    
    [Required(ErrorMessage = "Verification code is required")]
    public string VerificationCode { get; set; } = string.Empty;
    
    [Required(ErrorMessage = "New password is required")]
    [MinLength(8, ErrorMessage = "Password must be at least 8 characters")]
    public string NewPassword { get; set; } = string.Empty;
    
    public string CorrelationId { get; set; } = Guid.NewGuid().ToString();
}



/// <summary>
/// Request model for validating reset tokens.
/// </summary>
public class TokenValidationRequest
{
    [Required(ErrorMessage = "Token is required")]
    public string Token { get; set; } = string.Empty;
    
    public string? VerificationCode { get; set; }
    
    public string CorrelationId { get; set; } = Guid.NewGuid().ToString();
}
