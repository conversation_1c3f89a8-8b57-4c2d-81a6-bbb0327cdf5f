namespace PasswordHistoryValidator.Shared;

/// <summary>
/// Simplified rate limit information containing only the essential data used by the application.
/// </summary>
public class RateLimitInfo
{
    /// <summary>
    /// Whether the request is allowed or rate limited.
    /// </summary>
    public bool IsAllowed { get; set; }

    /// <summary>
    /// When the rate limit window resets and requests will be allowed again.
    /// </summary>
    public DateTime WindowResetTime { get; set; }
}
